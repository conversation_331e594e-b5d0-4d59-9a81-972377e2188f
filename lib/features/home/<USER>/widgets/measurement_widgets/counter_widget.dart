import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/photo_widget.dart'
    show PhotoUploadWidget;

class CounterWidget extends StatelessWidget {
  final Measurement measurement;
  final int value;
  final Function(int) onChanged;
  final bool showCameraIcon;
  final bool isCameraMandatory;
  final VoidCallback? onCameraTap;
  final bool isRequired;
  final String? errorText;

  const CounterWidget({
    super.key,
    required this.measurement,
    required this.value,
    required this.onChanged,
    this.showCameraIcon = false,
    this.isCameraMandatory = false,
    this.onCameraTap,
    this.isRequired = false,
    this.errorText,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(vertical: 4.0),
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: AppColors.black10,
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title with required indicator
          Row(
            children: [
              Expanded(
                child: Text(
                  measurement.measurementDescription ?? 'Counter',
                  style: textTheme.montserratTitleExtraSmall,
                ),
              ),
              if (isRequired)
                Container(
                  width: 16,
                  height: 16,
                  decoration: const BoxDecoration(
                    color: Colors.red,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.priority_high,
                    color: Colors.white,
                    size: 10,
                  ),
                ),
            ],
          ),
          const Gap(16),
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.blackTint2),
              borderRadius: BorderRadius.circular(8),
              color: Colors.white,
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Minus button
                InkWell(
                  onTap: value > 0 ? () => onChanged(value - 1) : null,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(8),
                    bottomLeft: Radius.circular(8),
                  ),
                  child: Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: value > 0
                          ? AppColors.primaryBlue
                          : AppColors.blackTint2,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(8),
                        bottomLeft: Radius.circular(8),
                      ),
                    ),
                    child: Icon(
                      Icons.remove,
                      color: value > 0 ? Colors.white : AppColors.blackTint1,
                      size: 20,
                    ),
                  ),
                ),
                // Value display
                Container(
                  width: 80,
                  height: 48,
                  decoration: const BoxDecoration(
                    color: Colors.white,
                  ),
                  child: Center(
                    child: Text(
                      value.toString(),
                      style: textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                        color: AppColors.black,
                      ),
                    ),
                  ),
                ),
                // Plus button
                InkWell(
                  onTap: () => onChanged(value + 1),
                  borderRadius: const BorderRadius.only(
                    topRight: Radius.circular(8),
                    bottomRight: Radius.circular(8),
                  ),
                  child: Container(
                    width: 48,
                    height: 48,
                    decoration: const BoxDecoration(
                      color: AppColors.primaryBlue,
                      borderRadius: BorderRadius.only(
                        topRight: Radius.circular(8),
                        bottomRight: Radius.circular(8),
                      ),
                    ),
                    child: const Icon(
                      Icons.add,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Camera section
          if (showCameraIcon) ...[
            const Gap(16),
            PhotoUploadWidget(
              onCameraPressed: () {
                if (onCameraTap != null) {
                  onCameraTap!();
                }
              },
            ),
            // Row(
            //   children: [
            //     // HI-RES Button
            //     Container(
            //       padding: const EdgeInsets.symmetric(
            //         horizontal: 12.0,
            //         vertical: 6.0,
            //       ),
            //       decoration: BoxDecoration(
            //         color: AppColors.lightGrey2,
            //         borderRadius: BorderRadius.circular(6),
            //         border: Border.all(
            //           color: AppColors.blackTint2,
            //           width: 1,
            //         ),
            //       ),
            //       child: Text(
            //         'HI-RES',
            //         style: textTheme.bodySmall?.copyWith(
            //           color: AppColors.blackTint1,
            //           fontSize: 11,
            //           fontWeight: FontWeight.w600,
            //         ),
            //       ),
            //     ),
            //     const Gap(12),
            //     // Camera Icon
            //     GestureDetector(
            //       onTap: onCameraTap,
            //       child: Container(
            //         width: 40,
            //         height: 40,
            //         decoration: BoxDecoration(
            //           color: isCameraMandatory ? Colors.amber : Colors.green,
            //           borderRadius: BorderRadius.circular(8),
            //         ),
            //         child: const Icon(
            //           Icons.camera_alt,
            //           color: Colors.white,
            //           size: 20,
            //         ),
            //       ),
            //     ),
            //   ],
            // ),
          ],
          if (errorText != null)
            Padding(
              padding: const EdgeInsets.only(top: 12.0),
              child: Text(
                errorText!,
                style: textTheme.bodySmall?.copyWith(
                  color: Colors.red,
                  fontSize: 12,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

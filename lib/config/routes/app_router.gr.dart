// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// AutoRouterGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:auto_route/auto_route.dart' as _i27;
import 'package:flutter/material.dart' as _i28;
import 'package:storetrack_app/features/auth/presentation/pages/login_page.dart'
    as _i8;
import 'package:storetrack_app/features/auth/presentation/pages/reset_password_page.dart'
    as _i17;
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as _i29;
import 'package:storetrack_app/features/home/<USER>/pages/assistant_page.dart'
    as _i1;
import 'package:storetrack_app/features/home/<USER>/pages/dashboard_holder_page.dart'
    as _i2;
import 'package:storetrack_app/features/home/<USER>/pages/dashboard_page.dart'
    as _i3;
import 'package:storetrack_app/features/home/<USER>/pages/form_page.dart'
    as _i5;
import 'package:storetrack_app/features/home/<USER>/pages/fqpd_page.dart'
    as _i4;
import 'package:storetrack_app/features/home/<USER>/pages/home_page.dart'
    as _i6;
import 'package:storetrack_app/features/home/<USER>/pages/journey_map_page.dart'
    as _i7;
import 'package:storetrack_app/features/home/<USER>/pages/more_page.dart'
    as _i10;
import 'package:storetrack_app/features/home/<USER>/pages/mpt_page.dart'
    as _i9;
import 'package:storetrack_app/features/home/<USER>/pages/notes_page.dart'
    as _i11;
import 'package:storetrack_app/features/home/<USER>/pages/pos_page.dart'
    as _i13;
import 'package:storetrack_app/features/home/<USER>/pages/profile_page.dart'
    as _i14;
import 'package:storetrack_app/features/home/<USER>/pages/qpmd_page.dart'
    as _i15;
import 'package:storetrack_app/features/home/<USER>/pages/question_page.dart'
    as _i16;
import 'package:storetrack_app/features/home/<USER>/pages/scheduled_page.dart'
    as _i18;
import 'package:storetrack_app/features/home/<USER>/pages/store_history_page.dart'
    as _i20;
import 'package:storetrack_app/features/home/<USER>/pages/store_info_page.dart'
    as _i21;
import 'package:storetrack_app/features/home/<USER>/pages/sub_header_page.dart'
    as _i22;
import 'package:storetrack_app/features/home/<USER>/pages/task_details_page.dart'
    as _i23;
import 'package:storetrack_app/features/home/<USER>/pages/todays_page.dart'
    as _i24;
import 'package:storetrack_app/features/home/<USER>/pages/unscheduled_page.dart'
    as _i25;
import 'package:storetrack_app/features/notification/presentation/pages/notification_page.dart'
    as _i12;
import 'package:storetrack_app/features/splash/presentation/pages/splash_page.dart'
    as _i19;
import 'package:storetrack_app/features/web_browser/presentation/pages/web_browser_page.dart'
    as _i26;

/// generated route for
/// [_i1.AssistantPage]
class AssistantRoute extends _i27.PageRouteInfo<void> {
  const AssistantRoute({List<_i27.PageRouteInfo>? children})
      : super(
          AssistantRoute.name,
          initialChildren: children,
        );

  static const String name = 'AssistantRoute';

  static _i27.PageInfo page = _i27.PageInfo(
    name,
    builder: (data) {
      return const _i1.AssistantPage();
    },
  );
}

/// generated route for
/// [_i2.DashboardHolderPage]
class DashboardHolderRoute extends _i27.PageRouteInfo<void> {
  const DashboardHolderRoute({List<_i27.PageRouteInfo>? children})
      : super(
          DashboardHolderRoute.name,
          initialChildren: children,
        );

  static const String name = 'DashboardHolderRoute';

  static _i27.PageInfo page = _i27.PageInfo(
    name,
    builder: (data) {
      return const _i2.DashboardHolderPage();
    },
  );
}

/// generated route for
/// [_i3.DashboardPage]
class DashboardRoute extends _i27.PageRouteInfo<void> {
  const DashboardRoute({List<_i27.PageRouteInfo>? children})
      : super(
          DashboardRoute.name,
          initialChildren: children,
        );

  static const String name = 'DashboardRoute';

  static _i27.PageInfo page = _i27.PageInfo(
    name,
    builder: (data) {
      return const _i3.DashboardPage();
    },
  );
}

/// generated route for
/// [_i4.FQPDPage]
class FQPDRoute extends _i27.PageRouteInfo<FQPDRouteArgs> {
  FQPDRoute({
    _i28.Key? key,
    _i29.Question? question,
    List<_i27.PageRouteInfo>? children,
  }) : super(
          FQPDRoute.name,
          args: FQPDRouteArgs(
            key: key,
            question: question,
          ),
          initialChildren: children,
        );

  static const String name = 'FQPDRoute';

  static _i27.PageInfo page = _i27.PageInfo(
    name,
    builder: (data) {
      final args =
          data.argsAs<FQPDRouteArgs>(orElse: () => const FQPDRouteArgs());
      return _i4.FQPDPage(
        key: args.key,
        question: args.question,
      );
    },
  );
}

class FQPDRouteArgs {
  const FQPDRouteArgs({
    this.key,
    this.question,
  });

  final _i28.Key? key;

  final _i29.Question? question;

  @override
  String toString() {
    return 'FQPDRouteArgs{key: $key, question: $question}';
  }
}

/// generated route for
/// [_i5.FormPage]
class FormRoute extends _i27.PageRouteInfo<FormRouteArgs> {
  FormRoute({
    _i28.Key? key,
    required _i29.TaskDetail task,
    List<_i27.PageRouteInfo>? children,
  }) : super(
          FormRoute.name,
          args: FormRouteArgs(
            key: key,
            task: task,
          ),
          initialChildren: children,
        );

  static const String name = 'FormRoute';

  static _i27.PageInfo page = _i27.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<FormRouteArgs>();
      return _i5.FormPage(
        key: args.key,
        task: args.task,
      );
    },
  );
}

class FormRouteArgs {
  const FormRouteArgs({
    this.key,
    required this.task,
  });

  final _i28.Key? key;

  final _i29.TaskDetail task;

  @override
  String toString() {
    return 'FormRouteArgs{key: $key, task: $task}';
  }
}

/// generated route for
/// [_i6.HomePage]
class HomeRoute extends _i27.PageRouteInfo<void> {
  const HomeRoute({List<_i27.PageRouteInfo>? children})
      : super(
          HomeRoute.name,
          initialChildren: children,
        );

  static const String name = 'HomeRoute';

  static _i27.PageInfo page = _i27.PageInfo(
    name,
    builder: (data) {
      return const _i6.HomePage();
    },
  );
}

/// generated route for
/// [_i7.JourneyMapPage]
class JourneyMapRoute extends _i27.PageRouteInfo<void> {
  const JourneyMapRoute({List<_i27.PageRouteInfo>? children})
      : super(
          JourneyMapRoute.name,
          initialChildren: children,
        );

  static const String name = 'JourneyMapRoute';

  static _i27.PageInfo page = _i27.PageInfo(
    name,
    builder: (data) {
      return const _i7.JourneyMapPage();
    },
  );
}

/// generated route for
/// [_i8.LoginPage]
class LoginRoute extends _i27.PageRouteInfo<void> {
  const LoginRoute({List<_i27.PageRouteInfo>? children})
      : super(
          LoginRoute.name,
          initialChildren: children,
        );

  static const String name = 'LoginRoute';

  static _i27.PageInfo page = _i27.PageInfo(
    name,
    builder: (data) {
      return const _i8.LoginPage();
    },
  );
}

/// generated route for
/// [_i9.MPTPage]
class MPTRoute extends _i27.PageRouteInfo<MPTRouteArgs> {
  MPTRoute({
    _i28.Key? key,
    String? taskId,
    String? formId,
    String? questionId,
    String? questionPartId,
    String? measurementId,
    String? combineTypeId,
    String? questionPartMultiId,
    List<String>? images,
    _i29.Question? question,
    int level = 2,
    List<_i27.PageRouteInfo>? children,
  }) : super(
          MPTRoute.name,
          args: MPTRouteArgs(
            key: key,
            taskId: taskId,
            formId: formId,
            questionId: questionId,
            questionPartId: questionPartId,
            measurementId: measurementId,
            combineTypeId: combineTypeId,
            questionPartMultiId: questionPartMultiId,
            images: images,
            question: question,
            level: level,
          ),
          initialChildren: children,
        );

  static const String name = 'MPTRoute';

  static _i27.PageInfo page = _i27.PageInfo(
    name,
    builder: (data) {
      final args =
          data.argsAs<MPTRouteArgs>(orElse: () => const MPTRouteArgs());
      return _i9.MPTPage(
        key: args.key,
        taskId: args.taskId,
        formId: args.formId,
        questionId: args.questionId,
        questionPartId: args.questionPartId,
        measurementId: args.measurementId,
        combineTypeId: args.combineTypeId,
        questionPartMultiId: args.questionPartMultiId,
        images: args.images,
        question: args.question,
        level: args.level,
      );
    },
  );
}

class MPTRouteArgs {
  const MPTRouteArgs({
    this.key,
    this.taskId,
    this.formId,
    this.questionId,
    this.questionPartId,
    this.measurementId,
    this.combineTypeId,
    this.questionPartMultiId,
    this.images,
    this.question,
    this.level = 2,
  });

  final _i28.Key? key;

  final String? taskId;

  final String? formId;

  final String? questionId;

  final String? questionPartId;

  final String? measurementId;

  final String? combineTypeId;

  final String? questionPartMultiId;

  final List<String>? images;

  final _i29.Question? question;

  final int level;

  @override
  String toString() {
    return 'MPTRouteArgs{key: $key, taskId: $taskId, formId: $formId, questionId: $questionId, questionPartId: $questionPartId, measurementId: $measurementId, combineTypeId: $combineTypeId, questionPartMultiId: $questionPartMultiId, images: $images, question: $question, level: $level}';
  }
}

/// generated route for
/// [_i10.MorePage]
class MoreRoute extends _i27.PageRouteInfo<void> {
  const MoreRoute({List<_i27.PageRouteInfo>? children})
      : super(
          MoreRoute.name,
          initialChildren: children,
        );

  static const String name = 'MoreRoute';

  static _i27.PageInfo page = _i27.PageInfo(
    name,
    builder: (data) {
      return const _i10.MorePage();
    },
  );
}

/// generated route for
/// [_i11.NotesPage]
class NotesRoute extends _i27.PageRouteInfo<NotesRouteArgs> {
  NotesRoute({
    _i28.Key? key,
    required _i29.TaskDetail task,
    List<_i27.PageRouteInfo>? children,
  }) : super(
          NotesRoute.name,
          args: NotesRouteArgs(
            key: key,
            task: task,
          ),
          initialChildren: children,
        );

  static const String name = 'NotesRoute';

  static _i27.PageInfo page = _i27.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<NotesRouteArgs>();
      return _i11.NotesPage(
        key: args.key,
        task: args.task,
      );
    },
  );
}

class NotesRouteArgs {
  const NotesRouteArgs({
    this.key,
    required this.task,
  });

  final _i28.Key? key;

  final _i29.TaskDetail task;

  @override
  String toString() {
    return 'NotesRouteArgs{key: $key, task: $task}';
  }
}

/// generated route for
/// [_i12.NotificationsPage]
class NotificationsRoute extends _i27.PageRouteInfo<void> {
  const NotificationsRoute({List<_i27.PageRouteInfo>? children})
      : super(
          NotificationsRoute.name,
          initialChildren: children,
        );

  static const String name = 'NotificationsRoute';

  static _i27.PageInfo page = _i27.PageInfo(
    name,
    builder: (data) {
      return const _i12.NotificationsPage();
    },
  );
}

/// generated route for
/// [_i13.PosPage]
class PosRoute extends _i27.PageRouteInfo<PosRouteArgs> {
  PosRoute({
    _i28.Key? key,
    _i29.TaskDetail? task,
    List<_i27.PageRouteInfo>? children,
  }) : super(
          PosRoute.name,
          args: PosRouteArgs(
            key: key,
            task: task,
          ),
          initialChildren: children,
        );

  static const String name = 'PosRoute';

  static _i27.PageInfo page = _i27.PageInfo(
    name,
    builder: (data) {
      final args =
          data.argsAs<PosRouteArgs>(orElse: () => const PosRouteArgs());
      return _i13.PosPage(
        key: args.key,
        task: args.task,
      );
    },
  );
}

class PosRouteArgs {
  const PosRouteArgs({
    this.key,
    this.task,
  });

  final _i28.Key? key;

  final _i29.TaskDetail? task;

  @override
  String toString() {
    return 'PosRouteArgs{key: $key, task: $task}';
  }
}

/// generated route for
/// [_i14.ProfilePage]
class ProfileRoute extends _i27.PageRouteInfo<void> {
  const ProfileRoute({List<_i27.PageRouteInfo>? children})
      : super(
          ProfileRoute.name,
          initialChildren: children,
        );

  static const String name = 'ProfileRoute';

  static _i27.PageInfo page = _i27.PageInfo(
    name,
    builder: (data) {
      return const _i14.ProfilePage();
    },
  );
}

/// generated route for
/// [_i15.QPMDPage]
class QPMDRoute extends _i27.PageRouteInfo<QPMDRouteArgs> {
  QPMDRoute({
    _i28.Key? key,
    _i29.Question? question,
    _i29.QuestionPart? questionPart,
    List<_i27.PageRouteInfo>? children,
  }) : super(
          QPMDRoute.name,
          args: QPMDRouteArgs(
            key: key,
            question: question,
            questionPart: questionPart,
          ),
          initialChildren: children,
        );

  static const String name = 'QPMDRoute';

  static _i27.PageInfo page = _i27.PageInfo(
    name,
    builder: (data) {
      final args =
          data.argsAs<QPMDRouteArgs>(orElse: () => const QPMDRouteArgs());
      return _i15.QPMDPage(
        key: args.key,
        question: args.question,
        questionPart: args.questionPart,
      );
    },
  );
}

class QPMDRouteArgs {
  const QPMDRouteArgs({
    this.key,
    this.question,
    this.questionPart,
  });

  final _i28.Key? key;

  final _i29.Question? question;

  final _i29.QuestionPart? questionPart;

  @override
  String toString() {
    return 'QPMDRouteArgs{key: $key, question: $question, questionPart: $questionPart}';
  }
}

/// generated route for
/// [_i16.QuestionPage]
class QuestionRoute extends _i27.PageRouteInfo<QuestionRouteArgs> {
  QuestionRoute({
    _i28.Key? key,
    required _i29.Form form,
    List<_i27.PageRouteInfo>? children,
  }) : super(
          QuestionRoute.name,
          args: QuestionRouteArgs(
            key: key,
            form: form,
          ),
          initialChildren: children,
        );

  static const String name = 'QuestionRoute';

  static _i27.PageInfo page = _i27.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<QuestionRouteArgs>();
      return _i16.QuestionPage(
        key: args.key,
        form: args.form,
      );
    },
  );
}

class QuestionRouteArgs {
  const QuestionRouteArgs({
    this.key,
    required this.form,
  });

  final _i28.Key? key;

  final _i29.Form form;

  @override
  String toString() {
    return 'QuestionRouteArgs{key: $key, form: $form}';
  }
}

/// generated route for
/// [_i17.ResetPasswordPage]
class ResetPasswordRoute extends _i27.PageRouteInfo<ResetPasswordRouteArgs> {
  ResetPasswordRoute({
    _i28.Key? key,
    required String email,
    List<_i27.PageRouteInfo>? children,
  }) : super(
          ResetPasswordRoute.name,
          args: ResetPasswordRouteArgs(
            key: key,
            email: email,
          ),
          initialChildren: children,
        );

  static const String name = 'ResetPasswordRoute';

  static _i27.PageInfo page = _i27.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<ResetPasswordRouteArgs>();
      return _i17.ResetPasswordPage(
        key: args.key,
        email: args.email,
      );
    },
  );
}

class ResetPasswordRouteArgs {
  const ResetPasswordRouteArgs({
    this.key,
    required this.email,
  });

  final _i28.Key? key;

  final String email;

  @override
  String toString() {
    return 'ResetPasswordRouteArgs{key: $key, email: $email}';
  }
}

/// generated route for
/// [_i18.SchedulePage]
class ScheduleRoute extends _i27.PageRouteInfo<void> {
  const ScheduleRoute({List<_i27.PageRouteInfo>? children})
      : super(
          ScheduleRoute.name,
          initialChildren: children,
        );

  static const String name = 'ScheduleRoute';

  static _i27.PageInfo page = _i27.PageInfo(
    name,
    builder: (data) {
      return const _i18.SchedulePage();
    },
  );
}

/// generated route for
/// [_i19.SplashPage]
class SplashRoute extends _i27.PageRouteInfo<void> {
  const SplashRoute({List<_i27.PageRouteInfo>? children})
      : super(
          SplashRoute.name,
          initialChildren: children,
        );

  static const String name = 'SplashRoute';

  static _i27.PageInfo page = _i27.PageInfo(
    name,
    builder: (data) {
      return const _i19.SplashPage();
    },
  );
}

/// generated route for
/// [_i20.StoreHistoryPage]
class StoreHistoryRoute extends _i27.PageRouteInfo<StoreHistoryRouteArgs> {
  StoreHistoryRoute({
    _i28.Key? key,
    required int storeId,
    required int taskId,
    List<_i27.PageRouteInfo>? children,
  }) : super(
          StoreHistoryRoute.name,
          args: StoreHistoryRouteArgs(
            key: key,
            storeId: storeId,
            taskId: taskId,
          ),
          initialChildren: children,
        );

  static const String name = 'StoreHistoryRoute';

  static _i27.PageInfo page = _i27.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<StoreHistoryRouteArgs>();
      return _i20.StoreHistoryPage(
        key: args.key,
        storeId: args.storeId,
        taskId: args.taskId,
      );
    },
  );
}

class StoreHistoryRouteArgs {
  const StoreHistoryRouteArgs({
    this.key,
    required this.storeId,
    required this.taskId,
  });

  final _i28.Key? key;

  final int storeId;

  final int taskId;

  @override
  String toString() {
    return 'StoreHistoryRouteArgs{key: $key, storeId: $storeId, taskId: $taskId}';
  }
}

/// generated route for
/// [_i21.StoreInfoPage]
class StoreInfoRoute extends _i27.PageRouteInfo<void> {
  const StoreInfoRoute({List<_i27.PageRouteInfo>? children})
      : super(
          StoreInfoRoute.name,
          initialChildren: children,
        );

  static const String name = 'StoreInfoRoute';

  static _i27.PageInfo page = _i27.PageInfo(
    name,
    builder: (data) {
      return const _i21.StoreInfoPage();
    },
  );
}

/// generated route for
/// [_i22.SubHeaderPage]
class SubHeaderRoute extends _i27.PageRouteInfo<SubHeaderRouteArgs> {
  SubHeaderRoute({
    _i28.Key? key,
    required String title,
    required List<_i29.QuestionPart> questionParts,
    required _i29.Question question,
    List<_i27.PageRouteInfo>? children,
  }) : super(
          SubHeaderRoute.name,
          args: SubHeaderRouteArgs(
            key: key,
            title: title,
            questionParts: questionParts,
            question: question,
          ),
          initialChildren: children,
        );

  static const String name = 'SubHeaderRoute';

  static _i27.PageInfo page = _i27.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<SubHeaderRouteArgs>();
      return _i22.SubHeaderPage(
        key: args.key,
        title: args.title,
        questionParts: args.questionParts,
        question: args.question,
      );
    },
  );
}

class SubHeaderRouteArgs {
  const SubHeaderRouteArgs({
    this.key,
    required this.title,
    required this.questionParts,
    required this.question,
  });

  final _i28.Key? key;

  final String title;

  final List<_i29.QuestionPart> questionParts;

  final _i29.Question question;

  @override
  String toString() {
    return 'SubHeaderRouteArgs{key: $key, title: $title, questionParts: $questionParts, question: $question}';
  }
}

/// generated route for
/// [_i23.TaskDetailsPage]
class TaskDetailsRoute extends _i27.PageRouteInfo<TaskDetailsRouteArgs> {
  TaskDetailsRoute({
    _i28.Key? key,
    required _i29.TaskDetail task,
    List<_i27.PageRouteInfo>? children,
  }) : super(
          TaskDetailsRoute.name,
          args: TaskDetailsRouteArgs(
            key: key,
            task: task,
          ),
          initialChildren: children,
        );

  static const String name = 'TaskDetailsRoute';

  static _i27.PageInfo page = _i27.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<TaskDetailsRouteArgs>();
      return _i23.TaskDetailsPage(
        key: args.key,
        task: args.task,
      );
    },
  );
}

class TaskDetailsRouteArgs {
  const TaskDetailsRouteArgs({
    this.key,
    required this.task,
  });

  final _i28.Key? key;

  final _i29.TaskDetail task;

  @override
  String toString() {
    return 'TaskDetailsRouteArgs{key: $key, task: $task}';
  }
}

/// generated route for
/// [_i24.TodayPage]
class TodayRoute extends _i27.PageRouteInfo<void> {
  const TodayRoute({List<_i27.PageRouteInfo>? children})
      : super(
          TodayRoute.name,
          initialChildren: children,
        );

  static const String name = 'TodayRoute';

  static _i27.PageInfo page = _i27.PageInfo(
    name,
    builder: (data) {
      return const _i24.TodayPage();
    },
  );
}

/// generated route for
/// [_i25.UnscheduledPage]
class UnscheduledRoute extends _i27.PageRouteInfo<void> {
  const UnscheduledRoute({List<_i27.PageRouteInfo>? children})
      : super(
          UnscheduledRoute.name,
          initialChildren: children,
        );

  static const String name = 'UnscheduledRoute';

  static _i27.PageInfo page = _i27.PageInfo(
    name,
    builder: (data) {
      return const _i25.UnscheduledPage();
    },
  );
}

/// generated route for
/// [_i26.WebBrowserPage]
class WebBrowserRoute extends _i27.PageRouteInfo<WebBrowserRouteArgs> {
  WebBrowserRoute({
    _i28.Key? key,
    required String url,
    List<_i27.PageRouteInfo>? children,
  }) : super(
          WebBrowserRoute.name,
          args: WebBrowserRouteArgs(
            key: key,
            url: url,
          ),
          initialChildren: children,
        );

  static const String name = 'WebBrowserRoute';

  static _i27.PageInfo page = _i27.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<WebBrowserRouteArgs>();
      return _i26.WebBrowserPage(
        key: args.key,
        url: args.url,
      );
    },
  );
}

class WebBrowserRouteArgs {
  const WebBrowserRouteArgs({
    this.key,
    required this.url,
  });

  final _i28.Key? key;

  final String url;

  @override
  String toString() {
    return 'WebBrowserRouteArgs{key: $key, url: $url}';
  }
}
